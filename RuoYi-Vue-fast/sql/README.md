# 人才管理系统 SQL 文件说明

## 文件结构重组说明

根据系统架构分析，我们对SQL文件进行了重新组织，将表结构定义和测试数据分离，以便更好地管理和维护。

## 核心文件

### 1. 系统基础文件
- **`ry_simplified.sql`** - 系统核心文件
  - 包含权限管理相关的表结构和数据
  - 包含系统用户、角色、菜单、部门等基础数据
  - 包含字典类型和字典数据
  - **此文件的权限相关数据保持不变**

### 2. 测试数据文件
- **`test_data_init.sql`** - 新创建的测试数据初始化文件
  - 包含所有业务表的测试数据
  - 数据之间保持外键关联的完整性
  - 模拟真实的业务场景
  - 包含企业、教师、学生、课程、培训等完整的业务数据

### 3. 表结构文件（已清理）
以下文件已移除初始化数据，只保留表结构定义：

#### 学生管理模块
- `student_info.sql` - 学生信息表
- `student_contract.sql` - 学生合同信息表
- `student_exam_score.sql` - 学生考试成绩表
- `student_growth_path.sql` - 学生成长路径表

#### 机构管理模块
- `org_company.sql` - 企业表
- `org_teacher.sql` - 教师表
- `org_major.sql` - 专业表
- `org_user.sql` - 组织人员表
- `org_structure.sql` - 组织架构表

#### 培训管理模块
- `training_program.sql` - 人才培养方案表
- `post_model.sql` - 岗位模型表
- `post_model_config.sql` - 岗位模型配置表
- `professional_ability.sql` - 职业能力表
- `pro_training.sql` - 培训项目表
- `pro_resource.sql` - 培训资源表
- `pro_mode.sql` - 培训模式表

#### 课程管理模块
- `course_school.sql` - 学校课程表
- `course_company.sql` - 企业课程表
- `std_course.sql` - 课程标准表
- `std_regulation.sql` - 制度管理表

#### 评价模块
- `evaluation_model.sql` - 评价模型表
- `evaluation_record.sql` - 评价记录表

#### 辅助模块
- `semester.sql` - 学期表

## 数据库初始化步骤

### 1. 基础系统初始化
```sql
-- 执行系统基础文件（包含权限数据）
source ry_simplified.sql;
```

### 2. 业务表结构创建
```sql
-- 按需执行各个业务表结构文件
source student_info.sql;
source student_contract.sql;
source org_company.sql;
source org_teacher.sql;
source org_major.sql;
source training_program.sql;
source post_model.sql;
source professional_ability.sql;
source course_school.sql;
source course_company.sql;
source evaluation_model.sql;
source semester.sql;
-- ... 其他表结构文件
```

### 3. 测试数据初始化
```sql
-- 执行测试数据文件
source test_data_init.sql;
```

## 数据关联关系

### 核心业务流程
1. **学生签约流程**：学生信息 → 合同信息 → 企业信息
2. **师生关系**：学生信息 → 校内教师/企业导师
3. **岗位企业关联**：岗位模型 ↔ 企业（多对多关系）
4. **评价体系**：评价题目 → 岗位模型/职业能力

### 权限体系（RBAC模型）
- 用户 → 角色 → 菜单权限
- 用户 → 部门
- 角色 → 部门权限

## 注意事项

1. **权限数据保护**：`ry_simplified.sql` 中的权限相关数据（用户、角色、菜单等）保持原状，确保系统正常运行
2. **数据完整性**：测试数据中的外键关联已经过验证，确保数据的完整性
3. **扩展性**：新的测试数据可以直接添加到 `test_data_init.sql` 文件中
4. **版本控制**：建议将表结构文件和测试数据文件分别进行版本控制

## 系统架构文档

详细的系统架构分析请参考：`database_structure_analysis.md`
