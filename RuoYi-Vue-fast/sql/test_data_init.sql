/*
 人才管理系统 - 测试数据初始化脚本
 
 说明：
 1. 本文件包含所有业务表的测试数据
 2. 权限相关数据保持在 ry_simplified.sql 中
 3. 数据之间保持外键关联的完整性
 4. 模拟真实的业务场景
 
 Date: 2025-08-05
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 初始化企业数据
-- ----------------------------
INSERT INTO `org_company` VALUES 
(1, '华为技术有限公司', '91440300279174282F', '任正非', '深圳市龙岗区坂田华为基地', '张经理', '13800138001', 15, 8, 'admin', sysdate(), '', null),
(2, '腾讯科技有限公司', '91440300708461136T', '马化腾', '深圳市南山区科技园', '李经理', '13800138002', 12, 6, 'admin', sysdate(), '', null),
(3, '阿里巴巴集团', '91330100MA27XF6T8K', '张勇', '杭州市余杭区文一西路969号', '王经理', '13800138003', 20, 10, 'admin', sysdate(), '', null),
(4, '百度在线网络技术公司', '91110000802100433B', '李彦宏', '北京市海淀区上地十街10号', '赵经理', '13800138004', 8, 4, 'admin', sysdate(), '', null),
(5, '字节跳动科技有限公司', '91110108MA01GKU05N', '梁汝波', '北京市海淀区北三环西路甲18号', '刘经理', '13800138005', 18, 9, 'admin', sysdate(), '', null);

-- ----------------------------
-- 初始化专业数据
-- ----------------------------
INSERT INTO `org_major` VALUES 
(1, '计算机应用技术', '培养掌握计算机应用技术基本理论和技能的高素质技术技能人才', 'admin', sysdate(), '', null),
(2, '大数据技术', '培养掌握大数据采集、存储、处理与分析技术的专业人才', 'admin', sysdate(), '', null),
(3, '软件技术', '培养掌握软件开发、测试、维护等技术的专业人才', 'admin', sysdate(), '', null),
(4, '网络技术', '培养掌握网络规划、建设、管理与维护技术的专业人才', 'admin', sysdate(), '', null),
(5, '人工智能技术', '培养掌握人工智能算法、应用开发技术的专业人才', 'admin', sysdate(), '', null);

-- ----------------------------
-- 初始化教师数据
-- ----------------------------
INSERT INTO `org_teacher` VALUES 
(1, '葛延宇', '校内教师', '计算机学院', '', '副教授', '男', '13901234567', 'admin', sysdate(), '', null),
(2, '沈咏东', '校内教师', '计算机学院', '', '讲师', '男', '13901234568', 'admin', sysdate(), '', null),
(3, '孙旭', '校内教师', '计算机学院', '', '教授', '男', '13901234569', 'admin', sysdate(), '', null),
(4, '康佳炜', '校内教师', '计算机学院', '', '副教授', '女', '13901234570', 'admin', sysdate(), '', null),
(5, '申海元', '校内教师', '计算机学院', '', '讲师', '男', '13901234571', 'admin', sysdate(), '', null),
(6, '庄国强', '企业导师', '', '华为技术有限公司', '高级工程师', '男', '13801234567', 'admin', sysdate(), '', null),
(7, '荀磊', '企业导师', '', '腾讯科技有限公司', '技术专家', '男', '13801234568', 'admin', sysdate(), '', null),
(8, '王建华', '企业导师', '', '阿里巴巴集团', '资深工程师', '男', '13801234569', 'admin', sysdate(), '', null),
(9, '李明', '企业导师', '', '百度在线网络技术公司', '技术总监', '男', '13801234570', 'admin', sysdate(), '', null),
(10, '张伟', '企业导师', '', '字节跳动科技有限公司', '高级架构师', '男', '13801234571', 'admin', sysdate(), '', null);

-- ----------------------------
-- 初始化岗位模型数据（保持原有数据）
-- ----------------------------
INSERT INTO `post_model` VALUES 
(1, '机械设计师', '负责机械产品的设计和开发工作', 'admin', sysdate(), '', null, null),
(2, '电气技术员', '负责电气设备的维护和技术支持', 'admin', sysdate(), '', null, null),
(3, '软件工程师', '负责软件系统的开发和维护', 'admin', sysdate(), '', null, null),
(4, '数据分析师', '负责数据分析和业务洞察', 'admin', sysdate(), '', null, null),
(5, '质量检测员', '负责产品质量检测和控制', 'admin', sysdate(), '', null, null),
(6, 'Java开发工程师', '负责Java后端系统开发', 'admin', sysdate(), '', null, null),
(7, '前端开发工程师', '负责Web前端界面开发', 'admin', sysdate(), '', null, null),
(8, '测试工程师', '负责软件产品测试工作', 'admin', sysdate(), '', null, null),
(9, '产品经理', '负责产品规划和需求管理', 'admin', sysdate(), '', null, null),
(10, 'AI算法工程师', '负责人工智能算法研发', 'admin', sysdate(), '', null, null);

-- ----------------------------
-- 初始化岗位模型与企业关联数据
-- ----------------------------
INSERT INTO `post_model_company` VALUES 
(1, 1), (1, 2), (2, 2), (2, 3), (3, 1), (3, 2), (3, 3), (3, 4), (3, 5),
(4, 1), (4, 3), (4, 4), (5, 1), (5, 3), (5, 5),
(6, 1), (6, 2), (6, 3), (6, 4), (6, 5),
(7, 2), (7, 3), (7, 4), (7, 5),
(8, 1), (8, 2), (8, 3), (8, 4), (8, 5),
(9, 2), (9, 3), (9, 4), (9, 5),
(10, 1), (10, 3), (10, 4), (10, 5);

-- ----------------------------
-- 初始化职业能力数据（扩展原有数据）
-- ----------------------------
INSERT INTO `professional_ability` VALUES 
(1, '机械设计师', '具备机械产品设计和开发的专业能力', 60, 'admin', sysdate(), '', null, null),
(2, '电气技术员', '具备电气设备维护和技术支持的专业能力', 60, 'admin', sysdate(), '', null, null),
(3, '软件工程师', '具备软件系统开发和维护的专业能力', 60, 'admin', sysdate(), '', null, null),
(4, '数据分析师', '具备数据分析和业务洞察的专业能力', 60, 'admin', sysdate(), '', null, null),
(5, '质量检测员', '具备产品质量检测和控制的专业能力', 60, 'admin', sysdate(), '', null, null),
(6, 'Java开发能力', '具备Java后端开发的专业技能', 65, 'admin', sysdate(), '', null, null),
(7, '前端开发能力', '具备Web前端开发的专业技能', 65, 'admin', sysdate(), '', null, null),
(8, '软件测试能力', '具备软件测试设计和执行的专业技能', 60, 'admin', sysdate(), '', null, null),
(9, '产品设计能力', '具备产品规划和需求分析的专业技能', 70, 'admin', sysdate(), '', null, null),
(10, 'AI算法能力', '具备人工智能算法设计和实现的专业技能', 75, 'admin', sysdate(), '', null, null);

-- ----------------------------
-- 初始化学校课程数据
-- ----------------------------
INSERT INTO `course_school` VALUES 
(1, 'Java程序设计', 'Java语言基础语法、面向对象编程、集合框架等核心技术', 64, 4, '掌握Java编程基础，具备面向对象编程思维', 'admin', sysdate(), '', null),
(2, '数据库原理与应用', 'MySQL数据库设计、SQL语言、数据库优化等', 48, 3, '掌握数据库设计和SQL编程技能', 'admin', sysdate(), '', null),
(3, 'Web前端技术', 'HTML、CSS、JavaScript、Vue.js等前端技术', 64, 4, '掌握现代Web前端开发技术', 'admin', sysdate(), '', null),
(4, '软件工程', '软件开发生命周期、需求分析、系统设计等', 48, 3, '掌握软件工程基本理论和方法', 'admin', sysdate(), '', null),
(5, '数据结构与算法', '线性表、树、图等数据结构及相关算法', 64, 4, '掌握常用数据结构和算法设计', 'admin', sysdate(), '', null);

-- ----------------------------
-- 初始化企业课程数据
-- ----------------------------
INSERT INTO `course_company` VALUES 
(1, 'Spring Boot企业级开发', '华为技术有限公司', 'Spring Boot框架、微服务架构、企业级应用开发', 80, 5, '掌握企业级Java开发技术', 'admin', sysdate(), '', null),
(2, '大数据处理技术', '阿里巴巴集团', 'Hadoop、Spark、Flink等大数据处理技术', 96, 6, '掌握大数据处理和分析技术', 'admin', sysdate(), '', null),
(3, '移动应用开发', '腾讯科技有限公司', 'Android、iOS移动应用开发技术', 80, 5, '掌握移动应用开发技能', 'admin', sysdate(), '', null),
(4, '人工智能应用', '百度在线网络技术公司', '机器学习、深度学习、AI应用开发', 96, 6, '掌握AI技术在实际项目中的应用', 'admin', sysdate(), '', null),
(5, '云计算技术', '字节跳动科技有限公司', '云服务、容器技术、DevOps等', 80, 5, '掌握云计算和DevOps技术', 'admin', sysdate(), '', null);

-- ----------------------------
-- 初始化学生合同信息数据（扩展原有数据）
-- ----------------------------
INSERT INTO `student_contract` VALUES
(1, '20240301001', '校企合作培训合同', '2024-03-01', 1, '华为技术有限公司', 'Java开发工程师', 8000.00, 'admin', sysdate(), '', null, ''),
(2, '20240301002', '实习培训合同', '2024-03-02', 2, '腾讯科技有限公司', '前端开发工程师', 7500.00, 'admin', sysdate(), '', null, ''),
(3, '20240301003', '就业培训合同', '2024-03-03', 3, '阿里巴巴集团', '数据分析师', 9000.00, 'admin', sysdate(), '', null, ''),
(4, '20240301004', '校企合作培训合同', '2024-03-04', 4, '百度在线网络技术公司', 'AI算法工程师', 10000.00, 'admin', sysdate(), '', null, ''),
(5, '20240301005', '实习培训合同', '2024-03-05', 5, '字节跳动科技有限公司', '测试工程师', 7000.00, 'admin', sysdate(), '', null, ''),
(6, '20240301006', '校企合作培训合同', '2024-03-06', 6, '华为技术有限公司', '软件工程师', 8500.00, 'admin', sysdate(), '', null, ''),
(7, '20240301007', '就业培训合同', '2024-03-07', 7, '腾讯科技有限公司', '产品经理', 9500.00, 'admin', sysdate(), '', null, ''),
(8, '20240301008', '实习培训合同', '2024-03-08', 8, '阿里巴巴集团', 'Java开发工程师', 8200.00, 'admin', sysdate(), '', null, ''),
(9, '20240301009', '校企合作培训合同', '2024-03-09', 9, '百度在线网络技术公司', '数据分析师', 8800.00, 'admin', sysdate(), '', null, ''),
(10, '20240301010', '就业培训合同', '2024-03-10', 10, '字节跳动科技有限公司', '前端开发工程师', 7800.00, 'admin', sysdate(), '', null, '');

-- ----------------------------
-- 初始化学生信息数据（扩展原有数据，保证关联完整性）
-- ----------------------------
INSERT INTO `student_info` VALUES
(1, '王尔康', '2450301537', '24计应五', '0', '2', '葛延宇', '庄国强', '计算机应用技术', 'Java开发工程师', '华为技术有限公司', 8000.00, 1, 2, 'admin', sysdate(), '', null, '优秀学生'),
(2, '丁豪谷', '2450301538', '24计应五', '0', '2', '沈咏东', '荀磊', '计算机应用技术', '前端开发工程师', '腾讯科技有限公司', 7500.00, 2, 2, 'admin', sysdate(), '', null, ''),
(3, '胡允', '2450301539', '24计应五', '0', '2', '孙旭', '王建华', '计算机应用技术', '数据分析师', '阿里巴巴集团', 9000.00, 3, 2, 'admin', sysdate(), '', null, ''),
(4, '李书君', '2450301540', '24计应五', '0', '2', '康佳炜', '李明', '计算机应用技术', 'AI算法工程师', '百度在线网络技术公司', 10000.00, 4, 2, 'admin', sysdate(), '', null, ''),
(5, '段向懿', '2450301541', '24计应五', '0', '2', '申海元', '张伟', '计算机应用技术', '测试工程师', '字节跳动科技有限公司', 7000.00, 5, 2, 'admin', sysdate(), '', null, ''),
(6, '赵雨', '2450401234', '24大数据二', '0', '2', '葛延宇', '庄国强', '大数据技术', '软件工程师', '华为技术有限公司', 8500.00, 6, 2, 'admin', sysdate(), '', null, ''),
(7, '李怡成', '2450401235', '24大数据二', '0', '2', '沈咏东', '荀磊', '大数据技术', '产品经理', '腾讯科技有限公司', 9500.00, 7, 2, 'admin', sysdate(), '', null, ''),
(8, '傅彭得', '2450401236', '24大数据二', '0', '2', '孙旭', '王建华', '大数据技术', 'Java开发工程师', '阿里巴巴集团', 8200.00, 8, 2, 'admin', sysdate(), '', null, ''),
(9, '刘大大', '2450401238', '24大数据二', '0', '2', '康佳炜', '李明', '大数据技术', '数据分析师', '百度在线网络技术公司', 8800.00, 9, 2, 'admin', sysdate(), '', null, ''),
(10, '杨静云', '2450401326', '24大数据二', '1', '2', '申海元', '张伟', '大数据技术', '前端开发工程师', '字节跳动科技有限公司', 7800.00, 10, 2, 'admin', sysdate(), '', null, ''),
(11, '张明', '2450501001', '24软件一', '0', '1', '葛延宇', '庄国强', '软件技术', 'Java开发工程师', '', null, null, 2, 'admin', sysdate(), '', null, '面试中'),
(12, '李华', '2450501002', '24软件一', '1', '0', '沈咏东', '', '软件技术', '', '', null, null, 2, 'admin', sysdate(), '', null, '待面试'),
(13, '王芳', '2450501003', '24软件一', '1', '1', '孙旭', '荀磊', '软件技术', '前端开发工程师', '', null, null, 2, 'admin', sysdate(), '', null, '面试失败'),
(14, '陈强', '2450501004', '24软件一', '0', '0', '康佳炜', '', '软件技术', '', '', null, null, 2, 'admin', sysdate(), '', null, '待面试'),
(15, '刘敏', '2450501005', '24软件一', '1', '0', '申海元', '', '软件技术', '', '', null, null, 2, 'admin', sysdate(), '', null, '待面试');

-- ----------------------------
-- 初始化培训方案数据（保持原有数据）
-- ----------------------------
INSERT INTO `training_program` VALUES
(1, '机械设计师', '培养具备机械产品设计和开发能力的专业人才', 3, 30, '培养具备机械产品设计和开发能力的专业人才，能够独立完成机械产品的设计、分析和优化工作。', '掌握机械设计基础理论，熟练使用CAD/CAM软件，具备产品设计和开发能力。', 0, 'admin', sysdate(), '', null, null),
(2, '电气技术员', '培养具备电气设备维护和技术支持能力的专业人才', 2, 25, '培养具备电气设备维护和技术支持能力的专业人才，能够独立完成电气设备的安装、调试和维护工作。', '掌握电气技术基础理论，熟练使用电气设计软件，具备设备维护和技术支持能力。', 0, 'admin', sysdate(), '', null, null),
(3, '软件工程师', '培养具备软件系统开发和维护能力的专业人才', 3, 40, '培养具备软件系统开发和维护能力的专业人才，能够独立完成软件系统的设计、开发和维护工作。', '掌握软件工程基础理论，熟练使用多种编程语言和开发工具，具备系统开发和维护能力。', 0, 'admin', sysdate(), '', null, null),
(4, '数据分析师', '培养具备数据分析和业务洞察能力的专业人才', 2, 20, '培养具备数据分析和业务洞察能力的专业人才，能够独立完成数据分析和业务洞察工作。', '掌握数据分析基础理论，熟练使用数据分析工具，具备数据分析和业务洞察能力。', 0, 'admin', sysdate(), '', null, null),
(5, '质量检测员', '培养具备产品质量检测和控制能力的专业人才', 1, 15, '培养具备产品质量检测和控制能力的专业人才，能够独立完成产品质量检测和控制工作。', '掌握质量检测基础理论，熟练使用检测设备和工具，具备质量检测和控制能力。', 0, 'admin', sysdate(), '', null, null),
(6, 'Java全栈开发工程师', '培养具备Java全栈开发能力的专业人才', 3, 50, '培养具备Java全栈开发能力的专业人才，能够独立完成企业级Java应用的设计、开发和部署工作。', '掌握Java核心技术、Spring生态、前端技术、数据库技术，具备全栈开发能力。', 0, 'admin', sysdate(), '', null, null),
(7, '大数据开发工程师', '培养具备大数据处理和分析能力的专业人才', 2, 30, '培养具备大数据处理和分析能力的专业人才，能够独立完成大数据平台搭建和数据分析工作。', '掌握Hadoop、Spark等大数据技术，具备数据处理和分析能力。', 0, 'admin', sysdate(), '', null, null),
(8, 'AI算法工程师', '培养具备人工智能算法开发能力的专业人才', 3, 25, '培养具备人工智能算法开发能力的专业人才，能够独立完成AI算法设计和应用开发工作。', '掌握机器学习、深度学习理论，具备AI算法开发和应用能力。', 0, 'admin', sysdate(), '', null, null);

-- ----------------------------
-- 初始化评价题库数据（保持原有数据）
-- ----------------------------
INSERT INTO `evaluation_question_bank` VALUES
(1, '电子信息', '0', 'admin', sysdate(), '', null, '电子信息相关题目'),
(2, '土木工程', '0', 'admin', sysdate(), '', null, '土木工程相关题目'),
(3, '化工工艺', '0', 'admin', sysdate(), '', null, '化工工艺相关题目'),
(4, '能源动力', '0', 'admin', sysdate(), '', null, '能源动力相关题目'),
(5, '材料科学', '0', 'admin', sysdate(), '', null, '材料科学相关题目'),
(6, '生物工程', '0', 'admin', sysdate(), '', null, '生物工程相关题目'),
(7, '环境科学', '0', 'admin', sysdate(), '', null, '环境科学相关题目'),
(8, '测绘工程', '0', 'admin', sysdate(), '', null, '测绘工程相关题目'),
(9, '计算机技术', '0', 'admin', sysdate(), '', null, '计算机技术相关题目'),
(10, '软件工程', '0', 'admin', sysdate(), '', null, '软件工程相关题目');

SET FOREIGN_KEY_CHECKS = 1;
