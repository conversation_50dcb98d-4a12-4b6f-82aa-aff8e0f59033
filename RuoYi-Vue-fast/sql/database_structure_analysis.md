# 人才管理系统数据库结构分析

## 系统概述
这是一个基于若依框架的人才管理系统，主要用于管理学生(学徒)的培养过程，包括学生信息、培训项目、课程管理、评价体系等功能。

## 核心模块架构

### 1. 权限管理模块（系统基础）
**核心表：**
- `sys_user` - 用户信息表
- `sys_role` - 角色信息表  
- `sys_menu` - 菜单权限表
- `sys_dept` - 部门表
- `sys_dict_type` / `sys_dict_data` - 字典管理

**关联表：**
- `sys_user_role` - 用户角色关联
- `sys_role_menu` - 角色菜单关联
- `sys_role_dept` - 角色部门关联

### 2. 学生管理模块
**核心表：**
- `student_info` - 学生信息表（系统核心实体）
- `student_contract` - 学生合同信息表
- `student_exam_score` - 学生考试成绩表
- `student_growth_path` - 学生成长路径表

### 3. 机构管理模块
**核心表：**
- `org_company` - 企业表
- `org_teacher` - 教师表
- `org_major` - 专业表
- `org_user` - 组织人员表
- `org_structure` - 组织架构表

### 4. 培训管理模块
**核心表：**
- `training_program` - 人才培养方案表
- `post_model` - 岗位模型表
- `professional_ability` - 职业能力表
- `pro_training` - 培训项目表
- `pro_resource` - 培训资源表
- `pro_mode` - 培训模式表

**关联表：**
- `post_model_company` - 岗位模型与企业关联表
- `post_model_config` - 岗位模型配置表

### 5. 课程管理模块
**核心表：**
- `course_school` - 学校课程表
- `course_company` - 企业课程表
- `std_course` - 课程标准表
- `std_regulation` - 制度管理表

### 6. 评价模块
**核心表：**
- `evaluation_question_bank` - 评价模型题库表
- `evaluation_question` - 评价模型题目表
- `evaluation_record` - 评价记录表

### 7. 辅助模块
**核心表：**
- `semester` - 学期表（时间维度管理）

## 关键关联关系

### 主要业务流程关联
1. **学生签约流程**：
   ```
   student_info.contract_id → student_contract.contract_id
   student_contract.company_name → org_company.company_name
   ```

2. **师生关系**：
   ```
   student_info.school_teacher → org_teacher.teacher_name
   student_info.company_teacher → org_teacher.teacher_name
   ```

3. **岗位企业关联**：
   ```
   post_model.post_id → post_model_company.post_id
   post_model_company.company_id → org_company.company_id
   ```

4. **评价体系关联**：
   ```
   evaluation_question.post_id → post_model.post_id
   evaluation_question.ability_id → professional_ability.ability_id
   evaluation_question.bank_id → evaluation_question_bank.bank_id
   ```

### 权限体系关联（RBAC模型）
```
sys_user → sys_user_role → sys_role → sys_role_menu → sys_menu
sys_user → sys_dept
sys_role → sys_role_dept → sys_dept
```

## 数据分类

### 权限相关数据（需保持现状）
- 系统用户、角色、菜单权限配置
- 部门结构
- 字典类型和字典数据

### 业务测试数据（需要重新组织）
- 学生信息及相关数据
- 企业、教师、专业等机构数据
- 培训方案、岗位模型、职业能力数据
- 课程、评价题库等教学数据

## 建议的数据初始化策略

1. **保留权限数据**：系统管理相关的用户、角色、菜单、字典等数据保持不变
2. **重构业务数据**：创建更完整、一致的业务测试数据集
3. **确保数据完整性**：新的测试数据要保证外键关联的完整性
4. **模拟真实场景**：测试数据应该模拟真实的业务场景和数据量
