-- ----------------------------
-- 评价记录表
-- ----------------------------
DROP TABLE IF EXISTS `evaluation_record`;
CREATE TABLE `evaluation_record` (
  `record_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '评价记录ID',
  `record_name` varchar(100) NOT NULL COMMENT '评价记录名称',
  `student_id` bigint(20) NOT NULL COMMENT '学生ID',
  `bank_id` bigint(20) NOT NULL COMMENT '题库ID',
  `bank_name` varchar(60) NOT NULL COMMENT '题库名称',
  `total_questions` int(11) NOT NULL DEFAULT 0 COMMENT '总题目数',
  `answered_questions` int(11) NOT NULL DEFAULT 0 COMMENT '已答题目数',
  `status` char(1) DEFAULT '0' COMMENT '状态（0进行中 1已完成）',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `complete_time` datetime DEFAULT NULL COMMENT '完成时间',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`record_id`),
  KEY `idx_student_id` (`student_id`),
  KEY `idx_bank_id` (`bank_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='评价记录表';

-- ----------------------------
-- 评价记录答题表
-- ----------------------------
DROP TABLE IF EXISTS `evaluation_record_answer`;
CREATE TABLE `evaluation_record_answer` (
  `answer_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '答题记录ID',
  `record_id` bigint(20) NOT NULL COMMENT '评价记录ID',
  `question_id` bigint(20) NOT NULL COMMENT '题目ID',
  `question_stem` varchar(500) NOT NULL COMMENT '题干',
  `question_type` char(1) NOT NULL COMMENT '题目类型（0单选题 1多选题）',
  `options` text NOT NULL COMMENT '选项（JSON格式）',
  `user_answer` varchar(500) DEFAULT NULL COMMENT '用户答案',
  `answer_time` datetime DEFAULT NULL COMMENT '答题时间',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`answer_id`),
  KEY `idx_record_id` (`record_id`),
  KEY `idx_question_id` (`question_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='评价记录答题表';

-- ----------------------------
-- 初始化评价记录数据
-- ----------------------------
INSERT INTO `evaluation_record` VALUES (1, '2025/05/28 12:31', 1, 1, '电子信息', 10, 10, '1', '2025-05-28 12:31:00', '2025-05-28 12:45:00', 'admin', sysdate(), '', null, '');
INSERT INTO `evaluation_record` VALUES (2, '2025/05/27 13:00', 1, 1, '电子信息', 8, 8, '1', '2025-05-27 13:00:00', '2025-05-27 13:15:00', 'admin', sysdate(), '', null, '');
INSERT INTO `evaluation_record` VALUES (3, '2025/05/26 13:00', 2, 2, '土木工程', 12, 12, '1', '2025-05-26 13:00:00', '2025-05-26 13:20:00', 'admin', sysdate(), '', null, '');
INSERT INTO `evaluation_record` VALUES (4, '2025/05/25 13:00', 3, 3, '化工工艺', 15, 15, '1', '2025-05-25 13:00:00', '2025-05-25 13:30:00', 'admin', sysdate(), '', null, '');

-- ----------------------------
-- 初始化评价记录答题数据
-- ----------------------------
INSERT INTO `evaluation_record_answer` VALUES (1, 1, 1, '题干文案超出列表最大长度时，需要显示省略号，并且鼠标悬停时显示完整内容', '0', '["A. 选项A", "B. 选项B", "C. 选项C", "D. 选项D"]', 'A', '2025-05-28 12:32:00', 'admin', sysdate(), '', null);
INSERT INTO `evaluation_record_answer` VALUES (2, 1, 2, '题干文案', '0', '["A. 选项A", "B. 选项B", "C. 选项C", "D. 选项D"]', 'B', '2025-05-28 12:33:00', 'admin', sysdate(), '', null);
INSERT INTO `evaluation_record_answer` VALUES (3, 1, 3, '题干文案', '1', '["A. 选项A", "B. 选项B", "C. 选项C", "D. 选项D"]', 'A,C', '2025-05-28 12:34:00', 'admin', sysdate(), '', null);
